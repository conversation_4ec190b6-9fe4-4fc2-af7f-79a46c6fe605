
/// Interface for speech recognition service
/// This abstract class defines methods that both mobile and web implementations must provide
abstract class SpeechServiceInterface {
  /// Initialize the speech recognition service
  Future<bool> initialize();

  /// Start listening for speech input
  Future<bool> startListening({
    Function(String text)? onResult,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
  });

  /// Stop listening for speech input
  Future<void> stopListening();

  /// Check if speech recognition is available
  Future<bool> isAvailable();

  /// Check if speech recognition is currently active
  bool get isListening;

  /// Get the final recognized text
  String get finalText;

  /// Dispose resources
  void dispose();
}