import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../providers/location_provider.dart';
import '../models/location.dart';
import '../widgets/location_list_item.dart';
import '../widgets/location_detail_widget.dart';
import '../widgets/add_edit_location_dialog.dart';
import '../widgets/map_with_search.dart';
import '../widgets/location_search_bar.dart';
import '../../../core/services/geolocation_service.dart';
import '../../../core/services/geocoding_service.dart';
import '../../../shared/widgets/pagination_controls.dart';

/// Screen for displaying and managing locations
class LocationsScreen extends ConsumerStatefulWidget {
  const LocationsScreen({super.key});

  @override
  ConsumerState<LocationsScreen> createState() => _LocationsScreenState();
}

class _LocationsScreenState extends ConsumerState<LocationsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final MapController _mapController = MapController();
  final bool _isMapInitialized = false;
  String? _searchQuery;
  bool _isAddLocationMode = false; // Track whether user is in add location mode

  @override
  void initState() {
    super.initState();
    // Initialize TabController
    _tabController = TabController(length: 2, vsync: this);

    // Fetch initial locations
    Future.microtask(() => ref.read(locationsProvider.notifier).fetchLocations());

    // Listen to tab changes to update UI accordingly
    _tabController.addListener(() {
      if (!mounted) return;
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Show dialog to add or edit a location
  void _showAddEditDialog({Location? location}) {
    showDialog(
      context: context,
      builder: (context) => AddEditLocationDialog(
        location: location,
        onSave: (data) async {
          try {
            if (location == null) {
              // Creating new location
              await ref.read(locationsProvider.notifier).addLocation(data);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Location added successfully')),
                );
              }
            } else {
              // Updating existing location
              await ref.read(locationsProvider.notifier).updateLocation(location.id, data);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Location updated successfully')),
                );
              }
            }
            if (mounted) {
              Navigator.of(context).pop();
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${e.toString()}')),
              );
            }
          }
        },
      ),
    );
  }

  /// Center map on a specific location
  void _centerMapOnLocation(Location location) {
    _mapController.move(
      LatLng(location.latitude, location.longitude),
      15.0, // Zoom level
    );
  }

  /// Center map on user's current location
  Future<void> _centerMapOnUserLocation() async {
    final coordinates = await ref.read(geolocationServiceProvider).getCurrentCoordinates();
    if (coordinates.latitude != null && coordinates.longitude != null) {
      _mapController.move(
        LatLng(coordinates.latitude!, coordinates.longitude!),
        15.0, // Zoom level
      );
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not get current location')),
        );
      }
    }
  }

  /// Handle search query changes
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.isNotEmpty ? query : null;
    });
    // Implement search functionality here
    // This would typically involve calling an API with the search query
  }

  /// Toggle add location mode
  void _toggleAddLocationMode() {
    setState(() {
      _isAddLocationMode = !_isAddLocationMode;
    });

    if (_isAddLocationMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tap anywhere on the map to add a location'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// Handle map tap when in add location mode
  void _handleMapTap(TapPosition tapPosition, LatLng latLng) {
    if (_isAddLocationMode) {
      // Show add location dialog with pre-filled coordinates
      _showAddLocationAtCoordinates(latLng.latitude, latLng.longitude);

      // Exit add location mode after tapping
      setState(() {
        _isAddLocationMode = false;
      });
    }
  }

  /// Show add location dialog with pre-filled coordinates
  void _showAddLocationAtCoordinates(double latitude, double longitude) {
    // Pre-fill data with the tapped coordinates
    showDialog(
      context: context,
      builder: (context) => AddEditLocationDialog(
        location: null, // New location
        onSave: (data) async {
          try {
            await ref.read(locationsProvider.notifier).addLocation(data);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Location added successfully')),
              );
            }
            if (mounted) {
              Navigator.of(context).pop();
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error: ${e.toString()}')),
              );
            }
          }
        },
        initialLatitude: latitude,
        initialLongitude: longitude,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationsProvider);
    final theme = Theme.of(context);

    // Filter locations if search query is present
    final locations = _searchQuery != null && _searchQuery!.isNotEmpty
        ? locationState.locations.where((loc) =>
            loc.name.toLowerCase().contains(_searchQuery!.toLowerCase()) ||
            (loc.address != null && loc.address!.toLowerCase().contains(_searchQuery!.toLowerCase())) ||
            (loc.keywords != null && loc.keywords!.any((k) => k.toLowerCase().contains(_searchQuery!.toLowerCase())))
          ).toList()
        : locationState.locations;
        
    // Get current location for the map
    Future<LatLng?> getCurrentLocation() async {
      final coordinates = await ref.read(geolocationServiceProvider).getCurrentCoordinates();
      if (coordinates.latitude != null && coordinates.longitude != null) {
        return LatLng(coordinates.latitude!, coordinates.longitude!);
      }
      return null;
    }

    return Scaffold(
      body: Column(
        children: [
          // Map with Search
          Expanded(
            child: MapWithSearch(
              locations: locations,
              onLocationSelected: (location) {
                ref.read(locationsProvider.notifier).selectLocation(location.id);
                _tabController.animateTo(1); // Switch to detail tab
              },
              onAddLocation: (latitude, longitude, address) {
                // Show add location dialog with pre-filled coordinates and address
                showDialog(
                  context: context,
                  builder: (context) => AddEditLocationDialog(
                    location: null, // New location
                    onSave: (data) async {
                      try {
                        await ref.read(locationsProvider.notifier).addLocation(data);
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Location added successfully')),
                          );
                          Navigator.of(context).pop();
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Error: ${e.toString()}')),
                          );
                        }
                      }
                    },
                    initialLatitude: latitude,
                    initialLongitude: longitude,
                    initialAddress: address,
                  ),
                );
              },
            ),
          ),
          
          // Loading indicator
          if (locationState.isLoading)
            const LinearProgressIndicator(),
          
          // Error message
          if (locationState.error != null)
            Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.red.withOpacity(0.1),
              child: Row(
                children: [
                  const Icon(LucideIcons.alertTriangle, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Error: ${locationState.error}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                  TextButton(
                    onPressed: () => ref.read(locationsProvider.notifier).fetchLocations(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'List'),
              Tab(text: 'Details'),
            ],
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // List view
                _buildLocationsList(locations, locationState),

                // Detail view
                _buildLocationDetail(locationState),
              ],
            ),
          ),
        ],
      ),
      // Removed floating action button as we now have the add location button in the search bar
    );
  }

  Widget _buildLocationsList(List<Location> locations, LocationState state) {
    if (state.isLoading && locations.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Text(
          'Error: ${state.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (locations.isEmpty) {
      return const Center(
        child: Text('No locations found. Add your first location!'),
      );
    }

    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: locations.length,
            itemBuilder: (context, index) {
              final location = locations[index];
              return LocationListItem(
                location: location,
                onTap: () {
                  ref.read(locationsProvider.notifier).selectLocation(location.id);
                  _centerMapOnLocation(location);
                  _tabController.animateTo(1); // Switch to detail tab
                },
              );
            },
          ),
        ),
        // Pagination controls if needed
        if (state.pagination != null && state.pagination!.totalPages > 1)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: PaginationControls(
              currentPage: state.pagination!.currentPage,
              totalPages: state.pagination!.totalPages,
              onPageChanged: (page) {
                ref.read(locationsProvider.notifier).fetchLocations(page: page);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildLocationDetail(LocationState state) {
    // Safety check to ensure we don't try to build details when loading or with errors
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Text(
          'Error: ${state.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    // If no location is selected, show a message
    if (state.selectedLocation == null) {
      return const Center(
        child: Text('Select a location to view details'),
      );
    }

    // Use a Builder to ensure we have the correct context
    return Builder(builder: (context) {
      return LocationDetailWidget(
        location: state.selectedLocation!,
        onEdit: () => _showAddEditDialog(location: state.selectedLocation),
        onDelete: () async {
        // Show confirmation dialog
        final shouldDelete = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Location'),
            content: Text('Are you sure you want to delete "${state.selectedLocation!.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
        );

        if (shouldDelete == true) {
          try {
            await ref.read(locationsProvider.notifier).deleteLocation(state.selectedLocation!.id);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Location deleted successfully')),
              );
              _tabController.animateTo(0); // Switch back to list tab
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Error deleting location: ${e.toString()}')),
              );
            }
          }
        }
      },
      );
    });
  }
}
