import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../../../core/services/speech_service.dart';

/// Enhanced modal dialog for recording voice input with live transcription
class RecordingModal extends StatefulWidget {
  const RecordingModal({super.key});

  @override
  State<RecordingModal> createState() => _RecordingModalState();
}

class _RecordingModalState extends State<RecordingModal> with TickerProviderStateMixin {
  final SpeechService _speechService = SpeechService();

  // Recording states - SIMPLIFIED FOR MANUAL CONTROL ONLY
  bool _isRecording = false;
  bool _isInitialized = false;
  bool _isStartingRecording = false;
  bool _isRecognitionPaused = false; // New flag to track if recognition is paused but can be restarted

  // Transcription - supporting live transcription
  String _finalTranscript = '';
  String _interimTranscript = '';
  String _error = '';

  // Timing
  int _recordingSeconds = 0;
  Timer? _recordingTimer;

  // Animation controllers
  late AnimationController _micPulseController;
  late Animation<double> _micPulseAnimation;

  // Enhanced waveform visualization
  List<double> _waveformHeights = List.filled(32, 4.0);
  double _currentSoundLevel = 0.0;

  // UI state
  bool _showBlink = true;
  Timer? _blinkTimer;
  Color _recordingColor = Colors.red.shade600;

  @override
  void initState() {
    super.initState();

    // Set up microphone pulse animation when recording
    _micPulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true);
    _micPulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _micPulseController, curve: Curves.easeInOut),
    );

    // Set up blinking animation for recording indicator
    _blinkTimer = Timer.periodic(const Duration(milliseconds: 600), (timer) {
      if (mounted) {
        setState(() {
          _showBlink = !_showBlink;
        });
      }
    });

    // Initialize speech and start recording automatically
    _initializeSpeechAndRecord();
  }

  @override
  void dispose() {
    _stopRecording();
    _recordingTimer?.cancel();
    _micPulseController.dispose();
    _blinkTimer?.cancel();
    _speechService.dispose();
    super.dispose();
  }

  /// Initialize the speech recognition and start recording automatically
  Future<void> _initializeSpeechAndRecord() async {
    // Show initializing state immediately
    setState(() {
      _isStartingRecording = true;
      _error = '';
    });

    try {
      final available = await _speechService.initialize();
      if (available) {
        setState(() {
          _isInitialized = true;
          _error = '';
        });

        // Automatically start recording after initialization
        _startRecording();
      } else {
        setState(() {
          _isStartingRecording = false;
          _error = 'Speech recognition not available on this device';
          _isInitialized = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing speech: $e');
      }
      setState(() {
        _isStartingRecording = false;
        _error = 'Error initializing speech recognition';
        _isInitialized = false;
      });
    }
  }

  /// Start recording with visual feedback
  void _startRecording() async {
    if (!_isInitialized) {
      setState(() {
        _error = 'Speech recognition not initialized';
      });
      return;
    }

    // Show starting state
    setState(() {
      _isStartingRecording = true;
      _isRecognitionPaused = false; // Reset paused state when starting
      // Clear transcript for fresh start
      _interimTranscript = '';
      _finalTranscript = '';
      _error = '';
    });

    // Start haptic feedback if available
    HapticFeedback.mediumImpact();

    final success = await _speechService.startListening(
      onResult: _onSpeechResult,
      onStatus: _onSpeechStatus,
      onError: _onSpeechError,
      onSoundLevel: _onSoundLevelChange,
    );

    if (success) {
      setState(() {
        _isStartingRecording = false;
        _isRecording = true;
        _interimTranscript = '';
        _error = '';
        _recordingSeconds = 0;
      });
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingSeconds++;

            // Change color every 15 seconds for better visual feedback
            if (_recordingSeconds % 15 == 0) {
              _recordingColor = _recordingSeconds % 30 == 0 ?
                  Colors.red.shade700 : Colors.orange.shade700;
            }
          });
        }
      });
    } else {
      setState(() {
        _isStartingRecording = false;
        _error = 'Failed to start speech recognition';
      });
    }
  }

  /// Continue recording while preserving existing transcript
  void _continueRecording() async {
    if (!_isInitialized) {
      setState(() {
        _error = 'Speech recognition not initialized';
      });
      return;
    }

    // Show starting state but preserve existing transcript
    setState(() {
      _isStartingRecording = true;
      _isRecognitionPaused = false;
      _error = '';
      // Keep existing _finalTranscript and _interimTranscript
    });

    // Start haptic feedback if available
    HapticFeedback.mediumImpact();

    final success = await _speechService.startListening(
      onResult: _onSpeechResult,
      onStatus: _onSpeechStatus,
      onError: _onSpeechError,
      onSoundLevel: _onSoundLevelChange,
    );

    if (success) {
      setState(() {
        _isStartingRecording = false;
        _isRecording = true;
        _error = '';
        // Continue from where we left off with the timer
        // _recordingSeconds keeps its current value
      });
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingSeconds++;

            // Change color every 15 seconds for better visual feedback
            if (_recordingSeconds % 15 == 0) {
              _recordingColor = _recordingSeconds % 30 == 0 ?
                  Colors.red.shade700 : Colors.orange.shade700;
            }
          });
        }
      });
    } else {
      setState(() {
        _isStartingRecording = false;
        _error = 'Failed to continue speech recognition';
      });
    }
  }

  /// Stop recording - MANUAL CONTROL ONLY
  void _stopRecording() {
    if (_isRecording) {
      _speechService.stopListening();
      _recordingTimer?.cancel();
      setState(() {
        _isRecording = false;

        // Combine existing final transcript with new interim transcript
        String newText = _speechService.finalText.isNotEmpty
            ? _speechService.finalText
            : _interimTranscript;

        if (_finalTranscript.isNotEmpty && newText.isNotEmpty) {
          // Append new text to existing transcript with proper spacing
          _finalTranscript = '${_finalTranscript.trim()} ${newText.trim()}';
        } else if (newText.isNotEmpty) {
          _finalTranscript = newText;
        }
        // If both are empty, _finalTranscript stays as is

        _interimTranscript = ''; // Clear interim since it's now part of final
        _waveformHeights = List.filled(32, 8.0);
        _currentSoundLevel = 0.0;
      });

      if (kDebugMode) {
        print('🛑 Recording stopped manually');
      }
    }
  }

  /// Handle speech recognition results - CONTINUOUS LIVE TRANSCRIPTION
  void _onSpeechResult(String text) {
    if (kDebugMode) {
      print('🎤 Speech result received: "$text"');
    }
    if (mounted) {
      setState(() {
        // If we have existing final transcript (from previous recording session),
        // we need to append new results to it
        if (_finalTranscript.isNotEmpty && text.isNotEmpty) {
          // Append new text with a space separator
          _interimTranscript = text;
          // The final combination will be handled when recording stops
        } else {
          _interimTranscript = text;
        }

        // Clear any previous errors when we get results
        if (text.isNotEmpty) {
          _error = '';
        }
      });
    }
  }

  /// Handle speech recognition status changes
  void _onSpeechStatus(String status) {
    if (kDebugMode) {
      print('Speech status: $status');
      print('Current state - isRecording: $_isRecording, finalTranscript: "$_finalTranscript", interimTranscript: "$_interimTranscript"');
    }

    if (status == 'listening') {
      // Speech recognition is actively listening
      if (!_isRecording) {
        setState(() {
          _isRecording = true;
          _isStartingRecording = false;
          _isRecognitionPaused = false;
          _error = '';
        });
      }
    } else if (status == 'notListening' || status == 'done') {
      // Android speech recognition has stopped due to its built-in timeout
      // Update UI to reflect this state, while preserving any transcription
      if (kDebugMode) {
        print("⚠️ Speech status: '$status' - recognition stopped");
        print("Current transcripts - final: '$_finalTranscript', interim: '$_interimTranscript'");
      }

      if (mounted) {
        _recordingTimer?.cancel(); // Stop the recording timer
        setState(() {
          _isRecording = false; // Properly reflect that recording has stopped
          _isRecognitionPaused = true; // Flag to show recognition is paused

          // Combine existing final transcript with new interim transcript
          if (_interimTranscript.isNotEmpty) {
            if (_finalTranscript.isNotEmpty) {
              _finalTranscript = '${_finalTranscript.trim()} ${_interimTranscript.trim()}';
            } else {
              _finalTranscript = _interimTranscript;
            }
            _interimTranscript = ''; // Clear interim since it's now part of final
            _error = "Recording paused. You can continue recording or save what was captured.";
            if (kDebugMode) {
              print("✅ Combined transcript: '$_finalTranscript'");
            }
          } else {
            _error = "Recording stopped - no speech detected. Tap 'Try Again' to restart.";
            if (kDebugMode) {
              print("❌ No transcript to preserve");
            }
          }
        });
      }
    } else if (status == 'error_no_match' || status == 'error_speech_timeout') {
      // Handle specific error types
      if (kDebugMode) {
        print("⚠️ Speech error status: $status");
      }

      if (mounted) {
        _recordingTimer?.cancel(); // Stop the recording timer
        setState(() {
          _isRecording = false;
          _isRecognitionPaused = true;

          // Give specific feedback based on error type and current state
          if (status == 'error_speech_timeout') {
            if (_interimTranscript.isNotEmpty) {
              // Combine existing final transcript with new interim transcript
              if (_finalTranscript.isNotEmpty) {
                _finalTranscript = '${_finalTranscript.trim()} ${_interimTranscript.trim()}';
              } else {
                _finalTranscript = _interimTranscript;
              }
              _interimTranscript = ''; // Clear interim since it's now part of final
              _error = "Speech timeout - but we captured some text. You can continue recording or save it.";
            } else {
              _error = "Speech timeout - no speech was detected. Try speaking louder or closer to the microphone.";
            }
          } else {
            _error = "Couldn't recognize speech clearly. Try speaking more clearly or in a quieter environment.";
          }
        });
      }
    }
  }

  /// Handle speech recognition errors
  void _onSpeechError(String error) {
    if (kDebugMode) {
      print('Speech error: $error');
    }

    if (mounted) {
      _recordingTimer?.cancel(); // Stop the recording timer
      setState(() {
        _isRecording = false;
        _isRecognitionPaused = true;

        // Preserve any captured text and provide helpful error message
        if (_interimTranscript.isNotEmpty) {
          // Combine existing final transcript with new interim transcript
          if (_finalTranscript.isNotEmpty) {
            _finalTranscript = '${_finalTranscript.trim()} ${_interimTranscript.trim()}';
          } else {
            _finalTranscript = _interimTranscript;
          }
          _interimTranscript = ''; // Clear interim since it's now part of final
          _error = "Error occurred but we captured some text: $error. You can continue recording or save it.";
        } else {
          _error = "Recording error: $error. Please try again.";
        }
      });
    }
  }

  /// Format the recording time as MM:SS
  String _formatRecordingTime() {
    final minutes = (_recordingSeconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_recordingSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  /// Handle sound level changes
  void _onSoundLevelChange(double level) {
    // Update the current sound level
    _currentSoundLevel = level;

    // Update waveform visualization based on sound level
    _updateWaveform();
  }

  /// Save the transcript and close the dialog
  void _saveTranscript() {
    _stopRecording();
    final textToSave = _finalTranscript.isEmpty ? _interimTranscript : _finalTranscript;
    Navigator.of(context).pop(textToSave);
  }

  /// Cancel recording and close the dialog
  void _cancelRecording() {
    _stopRecording();
    Navigator.of(context).pop();
  }

  void _updateWaveform() {
    if (!mounted) return;

    setState(() {
      // Create a more natural-looking waveform based on sound level
      // Center bars are taller, and amplitude is determined by current sound level
      const int numBars = 32;
      const double baseHeight = 8.0;
      final double maxAmplitude = 24.0 * _currentSoundLevel.clamp(0.0, 1.0);

      _waveformHeights = List.generate(numBars, (i) {
        // Create a bell curve pattern with random variation
        double position = i / (numBars - 1); // 0.0 to 1.0
        double bellCurve = 1.0 - (2.0 * position - 1.0) * (2.0 * position - 1.0); // 0.0 to 1.0 to 0.0

        // Add some randomness for a more natural look
        double randomFactor = 0.7 + (0.3 * (DateTime.now().millisecondsSinceEpoch % (i+5)) / 5);

        // Calculate final height and ensure it's always positive
        double height = baseHeight + (maxAmplitude * bellCurve * randomFactor);
        return height.clamp(4.0, 36.0); // Ensure safe bounds
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(LucideIcons.x),
            onPressed: _cancelRecording,
          ),
          title: Row(
            children: [
              Icon(_isRecording ? LucideIcons.mic : LucideIcons.mic,
                  color: _isRecording ? _recordingColor : null),
              const SizedBox(width: 8),
              Text(_isRecording ? 'Recording - Tap Stop When Done' :
                   _isStartingRecording ? 'Starting Recording...' :
                   _finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty ? 'Recording Complete' :
                   _isInitialized ? 'No Speech Detected' : 'Voice Input Unavailable',
                   style: TextStyle(
                     color: _isRecording ? _recordingColor : null,
                     fontWeight: _isRecording ? FontWeight.bold : null,
                   )),
            ],
          ),
          actions: [
            // Save button in app bar when there's text
            if (_isInitialized && (_interimTranscript.isNotEmpty || _finalTranscript.isNotEmpty))
              TextButton(
                onPressed: _saveTranscript,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Recording status with enhanced visual feedback
                  // Now shows a large, prominent recording indicator instead of a button
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer ripple effect for recording
                      if (_isRecording)
                        AnimatedBuilder(
                          animation: _micPulseController,
                          builder: (context, child) {
                            return Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _recordingColor.withValues(alpha: 0.1 * _micPulseAnimation.value),
                              ),
                            );
                          },
                        ),

                      // Main recording indicator with animation
                      AnimatedBuilder(
                        animation: _micPulseController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _isRecording ?
                                _micPulseAnimation.value :
                                (_isStartingRecording ? 0.95 : 1.0),
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _isRecording
                                    ? _recordingColor.withValues(alpha: 0.2)
                                    : _isStartingRecording
                                        ? Colors.orange.withValues(alpha: 0.2)
                                        : _finalTranscript.isNotEmpty
                                            ? Colors.green.shade100
                                            : Theme.of(context).colorScheme.primaryContainer,
                                border: Border.all(
                                  color: _isRecording
                                      ? _recordingColor
                                      : _isStartingRecording
                                          ? Colors.orange
                                          : _finalTranscript.isNotEmpty
                                              ? Colors.green.shade600
                                              : Theme.of(context).colorScheme.primary,
                                  width: 2.0,
                                ),
                              ),
                              child: Center(
                                child: Icon(
                                  _isRecording
                                      ? LucideIcons.mic
                                      : _isStartingRecording
                                          ? LucideIcons.loader
                                          : _finalTranscript.isNotEmpty
                                              ? LucideIcons.checkCircle
                                              : (_isInitialized ? LucideIcons.mic : LucideIcons.micOff),
                                  color: _isRecording
                                      ? _recordingColor
                                      : _isStartingRecording
                                          ? Colors.orange
                                          : _finalTranscript.isNotEmpty
                                              ? Colors.green.shade600
                                              : Theme.of(context).colorScheme.onPrimaryContainer,
                                  size: 40.0,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      // Extra visual indicator when starting
                      if (_isStartingRecording)
                        const CircularProgressIndicator(),
                    ],
                  ),
                  // Recording indicator with improved visual feedback
                  if (_isRecording)
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Blinking recording indicator
                          AnimatedOpacity(
                            opacity: _showBlink ? 1.0 : 0.2,
                            duration: const Duration(milliseconds: 300),
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: _recordingColor,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: _recordingColor.withValues(alpha: 0.5),
                                    blurRadius: 4,
                                    spreadRadius: 1
                                  )
                                ]
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recording...',
                            style: TextStyle(
                              color: _recordingColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Recording completed indicator
                  if (!_isRecording && !_isStartingRecording && _finalTranscript.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            LucideIcons.checkCircle,
                            color: Colors.green.shade600,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Recording completed',
                            style: TextStyle(
                              color: Colors.green.shade600,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Starting indicator
                  if (_isStartingRecording)
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0, bottom: 2.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Getting ready...',
                            style: TextStyle(
                              color: Colors.orange.shade700,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  // Recording time
                  if (_isRecording)
                    Padding(
                      padding: const EdgeInsets.only(top: 2.0, bottom: 2.0),
                      child: Text(
                        _formatRecordingTime(),
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                  // Enhanced audio waveform visualization
                  if (_isRecording)
                    Container(
                      margin: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                      height: 40,
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          final availableWidth = constraints.maxWidth;
                          final barCount = (availableWidth / 10).floor().clamp(8, 24);
                          final barWidth = (availableWidth / barCount) - 4;

                          return Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(barCount, (index) {
                              final rawHeight = _waveformHeights.length > index
                                  ? _waveformHeights[index]
                                  : 8.0;
                              // Ensure height is always positive and within reasonable bounds
                              final height = rawHeight.clamp(4.0, 36.0);
                              final safeBarWidth = barWidth.clamp(2.0, 20.0);

                              return Container(
                                width: safeBarWidth,
                                height: height,
                                margin: const EdgeInsets.symmetric(horizontal: 1),
                                decoration: BoxDecoration(
                                  color: _recordingColor.withValues(alpha: 0.7 + (0.3 * height / 40)),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              );
                            }),
                          );
                        },
                      ),
                    ),
                  // Transcript Display Area - Always visible for better UX
                  Container(
                    margin: const EdgeInsets.only(top: 16.0),
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: _isRecording
                            ? _recordingColor.withValues(alpha: 0.3)
                            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                        width: 1.0,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with status
                        Row(
                          children: [
                            Icon(
                              _isRecording
                                  ? LucideIcons.mic
                                  : _finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty
                                      ? LucideIcons.checkCircle
                                      : LucideIcons.micOff,
                              size: 16,
                              color: _isRecording
                                  ? _recordingColor
                                  : _finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty
                                      ? Colors.green.shade600
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              _isRecording
                                  ? 'Listening for speech...'
                                  : _finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty
                                      ? 'Speech captured'
                                      : 'No speech detected',
                              style: TextStyle(
                                fontSize: 12,
                                color: _isRecording
                                    ? _recordingColor
                                    : _finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty
                                        ? Colors.green.shade600
                                        : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Transcript content or helpful message
                        if (_finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty)
                          RichText(
                            text: TextSpan(
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                fontSize: 16,
                                height: 1.4,
                              ),
                              children: [
                                // Show final transcript (from previous recording sessions)
                                if (_finalTranscript.isNotEmpty)
                                  TextSpan(
                                    text: _finalTranscript,
                                    style: const TextStyle(fontWeight: FontWeight.normal),
                                  ),
                                // Add space between final and interim if both exist
                                if (_finalTranscript.isNotEmpty && _interimTranscript.isNotEmpty)
                                  const TextSpan(text: ' '),
                                // Show interim transcript (current recording session) with different styling
                                if (_interimTranscript.isNotEmpty)
                                  TextSpan(
                                    text: _interimTranscript,
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                                    ),
                                  ),
                              ],
                            ),
                          )
                        else if (_isRecording)
                          Text(
                            'Start speaking and your words will appear here...',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                              fontSize: 14,
                              fontStyle: FontStyle.italic,
                            ),
                          )
                        else
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'No speech was captured during recording.',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Tips:\n• Speak clearly and loudly\n• Check your microphone permissions\n• Ensure you\'re in a quiet environment\n• Try recording again',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                                  fontSize: 12,
                                  height: 1.4,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  // Error message
                  if (_error.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 16.0),
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Text(
                        _error,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              top: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                width: 1.0,
              ),
            ),
          ),
          child: Row(
            children: [
              // Cancel/Close button
              if (_isRecording)
                Expanded(
                  flex: 1,
                  child: OutlinedButton(
                    onPressed: _cancelRecording,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                    ),
                    child: const Text('Cancel'),
                  ),
                )
              else
                Expanded(
                  child: OutlinedButton(
                    onPressed: _cancelRecording,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                    ),
                    child: const Text('Close'),
                  ),
                ),

              const SizedBox(width: 16),

              // Main action button
              if (_isRecording)
                // STOP RECORDING - Prominent button when recording
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _stopRecording,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(LucideIcons.square, size: 18),
                        SizedBox(width: 8),
                        Text('Stop Recording', style: TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                )
              else if (_finalTranscript.isNotEmpty || _interimTranscript.isNotEmpty) ...[
                // Continue Recording button
                Expanded(
                  child: ElevatedButton(
                    onPressed: _continueRecording,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Colors.orange.shade600,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(LucideIcons.mic, size: 18),
                        SizedBox(width: 4),
                        Text('Continue'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Send button
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveTranscript,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Text('Send'),
                  ),
                ),
              ]
              else
                // Try Again button when no speech detected
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Reset and start recording again
                      setState(() {
                        _finalTranscript = '';
                        _interimTranscript = '';
                        _error = '';
                      });
                      _startRecording();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(LucideIcons.mic, size: 18),
                        SizedBox(width: 8),
                        Text('Try Again'),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
