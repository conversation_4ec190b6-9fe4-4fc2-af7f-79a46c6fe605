# Android Speech Timeout Fix

## Issue Summary

The FlashTasks mobile app was experiencing premature speech recognition timeouts on Android devices, where the speech recognition would stop after 8-13 seconds with `error_speech_timeout` even when voice was being successfully detected and transcribed.

## Root Cause Analysis

1. **Android SpeechRecognizer Limitations**: Android's built-in SpeechRecognizer has hardcoded timeout values that cannot be completely overridden by app-level configuration.

2. **Unrealistic Timeout Configuration**: The previous implementation tried to set extremely long timeouts (`Duration(hours: 1)` for `listenFor` and `Duration(minutes: 10)` for `pauseFor`) which Android's SpeechRecognizer ignores.

3. **UI State Inconsistency**: The UI was previously ignoring speech service status changes and showing "continuing recording (manual control only)" even when the underlying service had stopped.

## Implemented Solutions

### 1. Speech Service Configuration Improvements

**File**: `mobile/flashtasks_mobile/lib/src/core/services/speech_service_mobile.dart`

- **Realistic Timeouts**: Changed to more reasonable timeouts that work better with Android's constraints:
  - `listenFor: Duration(minutes: 5)` - Reasonable maximum duration
  - `pauseFor: Duration(seconds: 30)` - Longer pause detection but not excessive

- **Enhanced Options**: Added `enableHapticFeedback: true` to help signal activity to Android's speech recognition system.

- **Better Error Handling**: Maintained `cancelOnError: false` to let the UI handle errors gracefully rather than abruptly canceling.

### 2. UI Status Handling Improvements

**File**: `mobile/flashtasks_mobile/lib/src/features/dashboard/widgets/recording_modal.dart`

- **Accurate State Reflection**: The UI now properly reflects the actual speech service state instead of ignoring status changes.

- **Enhanced Status Handling**: Improved `_onSpeechStatus()` method to:
  - Properly stop recording timers when speech recognition stops
  - Preserve captured transcription when timeouts occur
  - Provide context-aware error messages based on whether text was captured

- **Better Error Messages**: Implemented specific feedback for different scenarios:
  - When text was captured before timeout: "Speech timeout - but we captured some text. You can save it or try again."
  - When no text was captured: "Speech timeout - no speech was detected. Try speaking louder or closer to the microphone."

- **Improved Error Recovery**: Enhanced `_onSpeechError()` to preserve any captured text and provide helpful guidance.

### 3. Interface Updates

**File**: `mobile/flashtasks_mobile/lib/src/core/services/speech_service_interface.dart`

- Added `onSoundLevel` parameter to the interface to support sound level monitoring across all implementations.

**File**: `mobile/flashtasks_mobile/lib/src/core/services/speech_service_web.dart`

- Updated web implementation to match the new interface signature.

## Key Behavioral Changes

### Before the Fix
- UI showed "continuing recording (manual control only)" even when speech service stopped
- Users were confused when recording appeared active but wasn't actually listening
- Captured text was sometimes lost when timeouts occurred
- Generic error messages didn't help users understand what happened

### After the Fix
- UI accurately reflects when speech recognition has stopped
- Clear, context-aware error messages guide users on next steps
- Captured text is preserved even when timeouts occur
- Users can choose to save partial transcriptions or restart recording

## User Experience Improvements

1. **Transparent Status**: Users now see exactly when recording has stopped due to timeouts.

2. **Text Preservation**: Any speech captured before a timeout is preserved and can be saved.

3. **Clear Actions**: Error messages include specific guidance on what users can do next.

4. **Restart Capability**: "Try Again" button allows easy restart of speech recognition after timeouts.

## Technical Notes

- Android's SpeechRecognizer has built-in timeout mechanisms that cannot be completely disabled
- The solution focuses on working within Android's constraints rather than fighting them
- Manual control is maintained - users still have full control over when to stop recording
- The fix maintains the original requirement of no auto-restart functionality

## Testing Recommendations

1. Test with various speech patterns (continuous speech, pauses, quiet speech)
2. Verify text preservation when timeouts occur
3. Confirm UI accurately reflects speech service state
4. Test restart functionality after timeouts
5. Validate error messages are helpful and actionable

## Future Considerations

- Monitor timeout frequency with the new configuration
- Consider implementing alternative speech recognition providers if Android limitations become too restrictive
- Explore using Android's newer speech recognition APIs if they provide better timeout control
