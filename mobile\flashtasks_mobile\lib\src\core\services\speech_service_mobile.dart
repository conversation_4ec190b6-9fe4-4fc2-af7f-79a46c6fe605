import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';

import 'speech_service_interface.dart';

/// Mobile implementation of SpeechServiceInterface using speech_to_text package
class SpeechService implements SpeechServiceInterface {
  final SpeechToText _speech = SpeechToText();
  String _finalText = '';
  Function(String)? _onStatusCallback;
  Function(double)? _onSoundLevelCallback;

  // Keep track of the max sound level to normalize values
  double _maxSoundLevel = 1.0;

  @override
  String get finalText => _finalText;

  @override
  bool get isListening => _speech.isListening;

  @override
  Future<bool> initialize() async {
    try {
      final bool available = await _speech.initialize(
        onError: (SpeechRecognitionError error) {
          debugPrint('Speech initialization error: ${error.errorMsg}, permanent: ${error.permanent}');
          if (_onStatusCallback != null) { // Also log errors via status if possible
            _onStatusCallback!('Error: ${error.errorMsg}');
          }
        },
        debugLogging: kDebugMode,
        onStatus: (status) {
          debugPrint('Speech status from initialize: $status');
          if (_onStatusCallback != null) {
            _onStatusCallback!(status);
          }
        },
      );
      return available;
    } catch (e) {
      debugPrint('Error initializing speech recognition: $e');
      return false;
    }
  }

  @override
  Future<bool> startListening({
    Function(String text)? onResult,
    Function(String status)? onStatus,
    Function(String error)? onError,
    Function(double level)? onSoundLevel,
  }) async {
    // Reset final text and sound level tracking
    _finalText = '';
    _maxSoundLevel = 1.0;
    _onStatusCallback = onStatus;
    _onSoundLevelCallback = onSoundLevel;

    try {
      await _speech.listen(
        onResult: (SpeechRecognitionResult result) {
          debugPrint('Speech result: "${result.recognizedWords}", final: ${result.finalResult}, isListening: ${_speech.isListening}');
          _finalText = result.recognizedWords;
          if (onResult != null) {
            onResult(result.recognizedWords);
          }
        },
        // Use more reasonable timeouts that work better with Android's constraints
        // Android SpeechRecognizer has built-in timeouts that can't be completely overridden
        listenFor: const Duration(minutes: 5), // Reasonable maximum duration
        pauseFor: const Duration(seconds: 59A), // Longer pause detection but not excessive
        onSoundLevelChange: (level) {
          // Use sound level for visual feedback and to signal activity to Android
          if (level > _maxSoundLevel) {
            _maxSoundLevel = level;
          }

          // Normalize the level to a 0.0-1.0 range
          double normalizedLevel = _maxSoundLevel > 0 ? level / _maxSoundLevel : 0.0;

          if (_onSoundLevelCallback != null) {
            _onSoundLevelCallback!(normalizedLevel.clamp(0.0, 1.0));
          }
        },
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false, // Don't cancel on error - let UI handle it
          listenMode: ListenMode.dictation, // Dictation mode for continuous listening
          // Enable sound level monitoring to help prevent timeouts
          enableHapticFeedback: true,
        ),
      );
      return true;
    } catch (e) {
      debugPrint('Error starting speech recognition (exception): $e');
      if (onError != null) {
        onError('Failed to start speech recognition: $e');
      }
      return false;
    }
  }

  @override
  Future<void> stopListening() async {
    await _speech.stop();
  }

  @override
  Future<bool> isAvailable() async {
    return _speech.isAvailable;
  }

  @override
  void dispose() {
    _speech.cancel();
  }
}