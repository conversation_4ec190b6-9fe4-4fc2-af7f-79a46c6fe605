# Continue Recording Feature

## Overview

Added a "Continue Recording" feature to handle Android's inconsistent speech recognition timeouts. When speech recognition stops unexpectedly (after 10+ seconds), users can now continue recording while preserving and appending to their existing transcript.

## Key Features

### 1. Continue Recording Button
- **When shown**: Appears when recording has stopped but transcript exists
- **Color**: Orange background to distinguish from other actions
- **Position**: Left side of button row, next to "Send" button
- **Icon**: Microphone icon with "Continue" text

### 2. Transcript Preservation
- **Automatic Combination**: When recording stops (timeout/error), interim transcript is automatically combined with final transcript
- **Proper Spacing**: Transcripts are combined with proper spacing between segments
- **Visual Distinction**: 
  - Final transcript (from previous sessions): Normal text
  - Interim transcript (current session): Italic, slightly faded

### 3. Seamless Continuation
- **Timer Continuity**: Recording timer continues from where it left off
- **State Preservation**: All recording state is maintained across sessions
- **Error Handling**: Graceful handling of continuation failures

## Implementation Details

### New Methods

#### `_continueRecording()`
```dart
void _continueRecording() async {
  // Preserves existing _finalTranscript and _interimTranscript
  // Starts new speech recognition session
  // Continues timer from current value
}
```

### Updated Methods

#### `_startRecording()`
- Now clears all transcripts for fresh start
- Resets timer to 0

#### `_stopRecording()`
- Combines final and interim transcripts with proper spacing
- Clears interim transcript after combination

#### `_onSpeechStatus()`, `_onSpeechError()`
- All status handlers now properly combine transcripts when recording stops
- Preserve captured text even during timeouts/errors

### UI Changes

#### Button Layout
- **Recording**: Shows "Stop Recording" button (red)
- **Has Transcript**: Shows "Continue" (orange) + "Send" (blue) buttons side by side
- **No Transcript**: Shows "Try Again" button (blue)

#### Transcript Display
- Uses `RichText` to show combined transcript with visual distinction
- Final transcript: Normal styling
- Interim transcript: Italic, slightly faded
- Proper spacing between segments

## User Experience

### Before Continue Feature
1. User starts recording
2. Android timeout occurs after 10+ seconds
3. User loses ability to add more content
4. Must start completely new recording

### After Continue Feature
1. User starts recording
2. Android timeout occurs after 10+ seconds
3. User sees "Continue" and "Send" buttons
4. User can tap "Continue" to add more content
5. New speech is appended to existing transcript
6. Process can repeat multiple times

## Error Messages

Updated error messages to reflect continue capability:

- **Timeout with text**: "Speech timeout - but we captured some text. You can continue recording or save it."
- **Timeout without text**: "Speech timeout - no speech was detected. Try speaking louder or closer to the microphone."
- **General pause**: "Recording paused. You can continue recording or save what was captured."

## Technical Benefits

1. **Handles Android Limitations**: Works around Android SpeechRecognizer's built-in timeouts
2. **Preserves User Input**: No loss of captured speech during timeouts
3. **Flexible Recording**: Allows for natural pauses and longer recordings
4. **Clear User Feedback**: Visual distinction between recording segments

## Usage Scenarios

### Long Dictation
- User dictating a long task description
- Android timeout occurs mid-sentence
- User continues seamlessly with "Continue" button

### Thoughtful Recording
- User pauses to think during recording
- Android interprets pause as end of speech
- User continues when ready without losing previous content

### Noisy Environment
- Background noise causes recognition issues
- User can retry specific segments
- Previous good segments are preserved

## Future Enhancements

1. **Visual Segments**: Could add visual indicators showing recording segments
2. **Edit Segments**: Allow editing individual recording segments
3. **Segment Timestamps**: Track when each segment was recorded
4. **Auto-Continue**: Optional setting to automatically restart after timeouts

## Testing Recommendations

1. Test with various speech patterns and pause lengths
2. Verify transcript combination works correctly
3. Confirm visual styling distinguishes segments
4. Test error scenarios and recovery
5. Validate button states and transitions
