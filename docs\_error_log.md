r ( 3013): Subscribed to topic: all_users
I/flutter ( 3013): Subscribed to topic: all_users
E/libEGL  ( 3013): called unimplemented OpenGL ES API
I/Choreographer( 3013): Skipped 219 frames!  The application may be doing too much work on its main thread.
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Start initialize
D/SpeechToTextPlugin( 3013): Checked permission
D/SpeechToTextPlugin( 3013): has permission, completing
D/SpeechToTextPlugin( 3013): completeInitialize
D/SpeechToTextPlugin( 3013): Testing recognition availability
D/SpeechToTextPlugin( 3013): sending result
D/SpeechToTextPlugin( 3013): leaving complete
D/SpeechToTextPlugin( 3013): leaving initializeIfPermitted
W/WindowOnBackDispatcher( 3013): OnBackInvokedCallback is not enabled for the application.
W/WindowOnBackDispatcher( 3013): Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
D/SpeechToTextPlugin( 3013): before setup intent
D/SpeechToTextPlugin( 3013): setupRecognizerIntent
D/SpeechToTextPlugin( 3013): after setup intent
D/SpeechToTextPlugin( 3013): Start listening
D/SpeechToTextPlugin( 3013): setupRecognizerIntent
D/SpeechToTextPlugin( 3013): Notify status:listening
I/flutter ( 3013): Speech status from initialize: listening
I/flutter ( 3013): Speech status: listening
D/SpeechToTextPlugin( 3013): Start listening done
D/SpeechToTextPlugin( 3013): Creating recognizer
D/SpeechToTextPlugin( 3013): Setting default listener
D/SpeechToTextPlugin( 3013): In RecognizerIntent apply
D/SpeechToTextPlugin( 3013): put model
D/SpeechToTextPlugin( 3013): put package
D/SpeechToTextPlugin( 3013): put partial
D/SpeechToTextPlugin( 3013): In RecognizerIntent apply
D/SpeechToTextPlugin( 3013): put model
D/SpeechToTextPlugin( 3013): put package
D/SpeechToTextPlugin( 3013): put partial
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / -2.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: ""
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: ""
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: ""
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: ""
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing"
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you"
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the"
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android"
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
I/ashtasks_mobile( 3013): Background concurrent mark compact GC freed 2680KB AllocSpace bytes, 25(1000KB) LOS objects, 49% free, 3530KB/7061KB, paused 33.020ms,22.662ms total 417.998ms
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a"
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please", final: false, isListening: true     
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please", final: false, isListening: true     
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about"       
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about"       
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about"       
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40"    
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds", final: false, isListening: true
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds"
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Notify status:notListening
I/flutter ( 3013): Speech status from initialize: notListening
I/flutter ( 3013): Speech status: notListening
I/flutter ( 3013): ! Speech status: 'notListening' - recognition stopped
D/SpeechToTextPlugin( 3013): Notify status:done

══╡ EXCEPTION CAUGHT BY RENDERING LIBRARY ╞═════════════════════════════════════════════════════════
The following assertion was thrown during performLayout():
RenderFlex children have non-zero flex but incoming width constraints are unbounded.
When a row is in a parent that does not provide a finite width constraint, for example if it is in a
horizontal scrollable, it will try to shrink-wrap its children along the horizontal axis. Setting a
flex on a child (e.g. using Expanded) indicates that the child is to expand to fill the remaining
space in the horizontal direction.
These two directives are mutually exclusive. If a parent is to shrink-wrap its child, the child
cannot simultaneously expand to fit its parent.
Consider setting mainAxisSize to MainAxisSize.min and using FlexFit.loose fits for the flexible
children (using Flexible rather than Expanded). This will allow the flexible children to size
themselves to less than the infinite remaining space they would otherwise be forced to take, and
then will cause the RenderFlex to shrink-wrap the children rather than expanding to fit the maximum
constraints provided by the parent.
If this message did not help you determine the problem, consider using debugDumpRenderTree():
  https://flutter.dev/to/debug-render-layer
  https://api.flutter.dev/flutter/rendering/debugDumpRenderTree.html
The affected RenderFlex is:
  RenderFlex#91b35 relayoutBoundary=up4 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE(creator: Row ← Row ← Padding ← DecoratedBox ← Container ← MediaQuery ← LayoutId-[<_ScaffoldSlot.bottomNavigationBar>] ←
  CustomMultiChildLayout ← _ActionsScope ← Actions ← AnimatedBuilder ← DefaultTextStyle ← ⋯, parentData: offset=Offset(0.0, 0.0); flex=null; fit=null (can use size), constraints: BoxConstraints(0.0<=w<=Infinity, 0.0<=h<=833.3), 
  size: MISSING, direction: horizontal, mainAxisAlignment: start, mainAxisSize: max, crossAxisAlignment: center, textDirection: ltr, verticalDirection: down, spacing: 0.0)
The creator information is set to:
  Row ← Row ← Padding ← DecoratedBox ← Container ← MediaQuery ←
  LayoutId-[<_ScaffoldSlot.bottomNavigationBar>] ← CustomMultiChildLayout ← _ActionsScope ← Actions
  ← AnimatedBuilder ← DefaultTextStyle ← ⋯
The nearest ancestor providing an unbounded width constraint is: RenderFlex#5555d relayoutBoundary=up3 NEEDS-LAYOUT NEEDS-COMPOSITING-BITS-UPDATE:
  creator: Row ← Padding ← DecoratedBox ← Container ← MediaQuery ←
    LayoutId-[<_ScaffoldSlot.bottomNavigationBar>] ← CustomMultiChildLayout ← _ActionsScope ← Actions
    ← AnimatedBuilder ← DefaultTextStyle ← AnimatedDefaultTextStyle ← ⋯
  parentData: offset=Offset(16.0, 17.0) (can use size)
  constraints: BoxConstraints(w=379.4, 0.0<=h<=833.3)
  size: Size(379.4, 52.0)
  direction: horizontal
  mainAxisAlignment: start
  mainAxisSize: max
  crossAxisAlignment: center
  textDirection: ltr
  verticalDirection: down
  spacing: 0.0
See also: https://flutter.dev/unbounded-constraints
If none of the above helps enough to fix this problem, please don't hesitate to file a bug:
  https://github.com/flutter/flutter/issues/new?template=2_bug.yml

The relevant error-causing widget was:
  Row
  Row:file:///C:/Users/<USER>/OneDrive%20-%20OSC%20AS/Repositories/TasksOrg/mobile/flashtasks_mobile/lib/src/features/dashboard/widgets/recording_modal.dart:912:17

When the exception was thrown, this was the stack:
#0      RenderFlex.performLayout.<anonymous closure> (package:flutter/src/rendering/flex.dart:1250:9)
#1      RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1253:6)
#2      RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
#3      ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:62:11)
#4      RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1161:28)
#5      RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1255:32)
#6      RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
#7      RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:243:12)
#8      RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
#9      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
#10     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
#11     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:180:12)
#12     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1059:11)
#13     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:249:7)
#14     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:419:14)
#15     RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2548:7)
#16     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1112:18)
#17     PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1125:15)
#18     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:616:23)
#19     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1231:13)
#20     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:482:5)
#21     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1442:15)
#22     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1355:9)
#23     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1208:5)
#24     _invoke (dart:ui/hooks.dart:316:13)
#25     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:428:5)
#26     _drawFrame (dart:ui/hooks.dart:288:31)

The following RenderObject was being processed when the exception was fired: RenderFlex#91b35 relayoutBoundary=up4 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
  creator: Row ← Row ← Padding ← DecoratedBox ← Container ← MediaQuery ←
    LayoutId-[<_ScaffoldSlot.bottomNavigationBar>] ← CustomMultiChildLayout ← _ActionsScope ← Actions
    ← AnimatedBuilder ← DefaultTextStyle ← ⋯
  parentData: offset=Offset(0.0, 0.0); flex=null; fit=null (can use size)
  constraints: BoxConstraints(0.0<=w<=Infinity, 0.0<=h<=833.3)
  size: MISSING
  direction: horizontal
  mainAxisAlignment: start
  mainAxisSize: max
  crossAxisAlignment: center
  textDirection: ltr
  verticalDirection: down
  spacing: 0.0
This RenderObject had the following descendants (showing up to depth 5):
    child 1: RenderSemanticsAnnotations#57a60 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
      child: _RenderInputPadding#13459 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
        child: RenderConstrainedBox#db43f NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
          child: RenderPhysicalShape#c30f2 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
            child: RenderCustomPaint#25ccd NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
    child 2: RenderConstrainedBox#ebf3f NEEDS-LAYOUT NEEDS-PAINT
    child 3: RenderSemanticsAnnotations#5964c NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
      child: _RenderInputPadding#194a7 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
        child: RenderConstrainedBox#af844 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
          child: RenderPhysicalShape#78738 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
            child: RenderCustomPaint#f8f2e NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
════════════════════════════════════════════════════════════════════════════════════════════════════

Another exception was thrown: RenderBox was not laid out: RenderFlex#91b35 relayoutBoundary=up4 NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
E/libEGL  ( 3013): called unimplemented OpenGL ES API
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): rmsDB -2.0 / 10.0
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds", final: false, isListening: false
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds"
D/SpeechToTextPlugin( 3013): Calling results callback
I/flutter ( 3013): Speech result: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds I", final: true, isListening: false
I/flutter ( 3013): 🎤 Speech result received: "I'm testing recording can you hear me I'm testing the recording on my Android emulator this is a test to confirm that the recording session is long please remind me in about 40 seconds I"
I/flutter ( 3013): Speech status from initialize: done
I/flutter ( 3013): Speech status: done
I/flutter ( 3013): ! Speech status: 'done' - recognition stopped
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
E/libEGL  ( 3013): called unimplemented OpenGL ES API
Another exception was thrown: Cannot hit test a render box with no size.
